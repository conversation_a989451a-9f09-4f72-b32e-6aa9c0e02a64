/* About Page Specific Styles */

/* About Hero */
.about-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Journey Section */
.journey-section {
    padding: 6rem 0;
    background: var(--primary-bg);
}

.journey-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.journey-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.journey-item.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.journey-icon {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 20px;
    flex-shrink: 0;
    box-shadow: var(--shadow-medium);
}

.journey-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.journey-info p {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* AI Brain Visual */
.journey-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-brain {
    position: relative;
    width: 300px;
    height: 300px;
}

.brain-node {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    animation: brainPulse 3s ease-in-out infinite;
    animation-delay: var(--delay);
    box-shadow: var(--shadow-medium);
}

.brain-node:nth-child(1) { top: 20%; left: 20%; }
.brain-node:nth-child(2) { top: 10%; right: 30%; }
.brain-node:nth-child(3) { bottom: 30%; left: 10%; }
.brain-node:nth-child(4) { bottom: 20%; right: 20%; }
.brain-node:nth-child(5) { top: 50%; left: 50%; transform: translate(-50%, -50%); }

@keyframes brainPulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(79, 70, 229, 0);
    }
}

/* Philosophy Section */
.philosophy-section {
    padding: 6rem 0;
    background: var(--secondary-bg);
}

.philosophy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.philosophy-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(30px);
}

.philosophy-card.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.philosophy-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(16, 185, 129, 0.1));
    border-radius: 20px;
}

.philosophy-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.philosophy-card p {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Web3 Vision Section */
.web3-vision-section {
    padding: 6rem 0;
    background: var(--primary-bg);
}

.vision-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.vision-item {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.vision-item.aos-animate {
    opacity: 1;
    transform: translateX(0);
}

.vision-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: var(--shadow-medium);
}

.vision-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.vision-info p {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Web3 Ecosystem Visual */
.vision-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.web3-ecosystem {
    position: relative;
    width: 300px;
    height: 300px;
}

.ecosystem-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    box-shadow: var(--shadow-large);
    animation: centerPulse 2s ease-in-out infinite;
}

@keyframes centerPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

.ecosystem-ring {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    animation: rotate 20s linear infinite;
}

.ecosystem-node {
    position: absolute;
    width: 60px;
    height: 60px;
    background: white;
    border: 3px solid var(--tech-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--tech-blue);
    box-shadow: var(--shadow-medium);
}

.ecosystem-node.nft { top: 0; left: 50%; transform: translateX(-50%); }
.ecosystem-node.blockchain { top: 25%; right: 0; }
.ecosystem-node.defi { bottom: 25%; right: 0; }
.ecosystem-node.dao { bottom: 0; left: 50%; transform: translateX(-50%); }

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Roadmap Section */
.roadmap-section {
    padding: 6rem 0;
    background: var(--secondary-bg);
}

.roadmap-timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.roadmap-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-light);
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.timeline-item.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.timeline-marker {
    position: absolute;
    left: 20px;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--border-light);
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: var(--shadow-soft);
}

.timeline-item.completed .timeline-marker {
    background: var(--ai-green);
}

.timeline-item.active .timeline-marker {
    background: var(--tech-blue);
    animation: pulse 2s ease-in-out infinite;
}

.timeline-content {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border-light);
}

.timeline-date {
    color: var(--tech-blue);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.timeline-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.timeline-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Section */
.contact-section {
    padding: 6rem 0;
    background: var(--primary-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-info p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--secondary-bg);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.contact-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 12px;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
}

/* Contact Animation */
.contact-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.contact-animation {
    position: relative;
    width: 300px;
    height: 300px;
}

.floating-element {
    position: absolute;
    font-size: 3rem;
    animation: contactFloat 6s ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-element:nth-child(1) { top: 20%; left: 20%; }
.floating-element:nth-child(2) { top: 20%; right: 20%; }
.floating-element:nth-child(3) { bottom: 20%; left: 20%; }
.floating-element:nth-child(4) { bottom: 20%; right: 20%; }

@keyframes contactFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(5deg); }
    50% { transform: translateY(0px) rotate(0deg); }
    75% { transform: translateY(10px) rotate(-5deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .journey-content,
    .vision-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .philosophy-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .journey-item,
    .vision-item {
        flex-direction: column;
        text-align: center;
    }
    
    .journey-icon,
    .vision-number {
        align-self: center;
    }
    
    .timeline-item {
        padding-left: 60px;
    }
    
    .roadmap-timeline::before {
        left: 20px;
    }
    
    .timeline-marker {
        left: 10px;
    }
}
