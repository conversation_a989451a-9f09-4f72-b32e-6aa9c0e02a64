<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>「慧视」AI金融洞察引擎 - AI驱动的金融智能分析平台</title>
    <meta name="description" content="「慧视」AI金融洞察引擎，通过深度学习技术发现市场深层模式，提供K线模式洞察和金融报告情绪分析。">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-icon">
                    <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" stroke="url(#gradient1)" stroke-width="2"/>
                        <path d="M12 20L18 26L28 14" stroke="url(#gradient1)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4F46E5"/>
                                <stop offset="100%" style="stop-color:#06B6D4"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <span class="logo-text">「慧视」AI金融洞察引擎</span>
            </div>
            <div class="nav-links">
                <a href="#home" class="nav-link active">首页</a>
                <a href="demo.html" class="nav-link">AI洞察演示</a>
                <a href="about.html" class="nav-link">关于我们</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="grid-pattern"></div>
            <div class="floating-particles"></div>
        </div>
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="gradient-text">「慧视」AI金融洞察引擎</span>
                </h1>
                <p class="hero-subtitle">AI驱动深度学习，发现市场深层模式</p>
                <p class="hero-description">
                    通过先进的人工智能技术，将复杂的金融数据转化为清晰的洞察，
                    让AI成为您探索金融市场的智能向导。
                </p>
                <div class="hero-cta">
                    <a href="demo.html" class="cta-button primary">
                        <span>探索AI洞察</span>
                        <svg class="cta-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                </div>
                <p class="disclaimer">AI分析仅供技术演示，不构成投资建议。</p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="8" y="32" width="4" height="8" fill="url(#featureGradient1)"/>
                            <rect x="16" y="24" width="4" height="16" fill="url(#featureGradient1)"/>
                            <rect x="24" y="16" width="4" height="24" fill="url(#featureGradient1)"/>
                            <rect x="32" y="20" width="4" height="20" fill="url(#featureGradient1)"/>
                            <path d="M8 16L16 12L24 8L32 12L40 8" stroke="url(#featureGradient1)" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="featureGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4F46E5"/>
                                    <stop offset="100%" style="stop-color:#06B6D4"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3 class="feature-title">K线模式洞察</h3>
                    <p class="feature-description">
                        AI深度学习识别经典K线形态，发现隐藏的市场信号和趋势转折点。
                    </p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24" r="16" stroke="url(#featureGradient2)" stroke-width="2"/>
                            <path d="M16 24C16 24 20 20 24 20C28 20 32 24 32 24" stroke="url(#featureGradient2)" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="20" cy="20" r="2" fill="url(#featureGradient2)"/>
                            <circle cx="28" cy="28" r="2" fill="url(#featureGradient2)"/>
                            <defs>
                                <linearGradient id="featureGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#10B981"/>
                                    <stop offset="100%" style="stop-color:#06B6D4"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3 class="feature-title">金融叙事引擎</h3>
                    <p class="feature-description">
                        智能提炼财报核心要点，精准识别市场情绪，让复杂信息一目了然。
                    </p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="8" y="8" width="32" height="32" rx="4" stroke="url(#featureGradient3)" stroke-width="2"/>
                            <path d="M16 20L20 24L32 16" stroke="url(#featureGradient3)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="2" fill="url(#featureGradient3)"/>
                            <circle cx="36" cy="12" r="2" fill="url(#featureGradient3)"/>
                            <circle cx="12" cy="36" r="2" fill="url(#featureGradient3)"/>
                            <circle cx="36" cy="36" r="2" fill="url(#featureGradient3)"/>
                            <defs>
                                <linearGradient id="featureGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#F59E0B"/>
                                    <stop offset="100%" style="stop-color:#EF4444"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3 class="feature-title">智能概率推演</h3>
                    <p class="feature-description">
                        基于历史数据模式，AI计算趋势概率，为决策提供数据支撑。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Web3 Vision Section -->
    <section class="web3-vision">
        <div class="container">
            <div class="web3-content">
                <div class="web3-text">
                    <h2 class="section-title">探索AI与Web3的未来</h2>
                    <p class="web3-description">
                        我们正在构建一个革命性的愿景：将AI洞察转化为可验证、可流通的数字资产。
                        通过区块链技术，每一个AI分析结果都将成为独特的数字资产，
                        在去中心化的网络中实现价值流通与验证。
                    </p>
                    <div class="web3-features">
                        <div class="web3-feature">
                            <span class="web3-icon">🔗</span>
                            <span>AI洞察NFT化</span>
                        </div>
                        <div class="web3-feature">
                            <span class="web3-icon">🌐</span>
                            <span>去中心化验证</span>
                        </div>
                        <div class="web3-feature">
                            <span class="web3-icon">💎</span>
                            <span>数字资产流通</span>
                        </div>
                    </div>
                </div>
                <div class="web3-visual">
                    <div class="blockchain-network">
                        <div class="network-node" style="--delay: 0s"></div>
                        <div class="network-node" style="--delay: 0.5s"></div>
                        <div class="network-node" style="--delay: 1s"></div>
                        <div class="network-node" style="--delay: 1.5s"></div>
                        <div class="network-node" style="--delay: 2s"></div>
                        <div class="network-connection"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-disclaimer">
                    <h3>重要声明</h3>
                    <p>本网站内容仅为AI技术演示，不构成任何投资建议。所有分析结果仅供学习和研究使用，投资有风险，决策需谨慎。</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>产品</h4>
                        <a href="demo.html">AI洞察演示</a>
                        <a href="#features">核心功能</a>
                    </div>
                    <div class="footer-section">
                        <h4>公司</h4>
                        <a href="about.html">关于我们</a>
                        <a href="about.html#vision">AI理念</a>
                    </div>
                    <div class="footer-section">
                        <h4>联系</h4>
                        <a href="mailto:<EMAIL>">联系我们</a>
                        <a href="#privacy">隐私政策</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 「慧视」AI金融洞察引擎. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
