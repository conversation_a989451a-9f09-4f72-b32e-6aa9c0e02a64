/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-bg: #fefefe;
    --secondary-bg: #f8fafc;
    --tech-blue: #4F46E5;
    --ai-green: #10B981;
    --warm-gold: #F59E0B;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --border-light: #e5e7eb;
    --glass-bg: rgba(255, 255, 255, 0.8);
    --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--tech-blue) 0%, var(--ai-green) 100%);
    --gradient-warm: linear-gradient(135deg, var(--warm-gold) 0%, var(--tech-blue) 100%);
    --gradient-text: linear-gradient(135deg, var(--tech-blue) 0%, var(--ai-green) 50%, var(--warm-gold) 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--primary-bg);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 32px;
    height: 32px;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--tech-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(79, 70, 229, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(79, 70, 229, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridFloat 20s ease-in-out infinite;
}

@keyframes gridFloat {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(10px, 10px); }
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
    animation: float 15s ease-in-out infinite;
}

.floating-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-particles::after {
    bottom: 20%;
    right: 10%;
    animation-delay: 7s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.gradient-text {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(10deg); }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
}

.hero-cta {
    margin-bottom: 2rem;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.125rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.cta-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.cta-button:hover .cta-icon {
    transform: translateX(4px);
}

.disclaimer {
    color: var(--text-light);
    font-size: 0.875rem;
    font-style: italic;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--secondary-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Web3 Vision Section */
.web3-vision {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.web3-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.web3-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.web3-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.web3-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
}

.web3-feature:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.web3-icon {
    font-size: 1.5rem;
}

.web3-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.blockchain-network {
    position: relative;
    width: 300px;
    height: 300px;
}

.network-node {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    animation-delay: var(--delay);
}

.network-node:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); }
.network-node:nth-child(2) { top: 25%; right: 0; }
.network-node:nth-child(3) { bottom: 25%; right: 0; }
.network-node:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); }
.network-node:nth-child(5) { top: 25%; left: 0; }

@keyframes pulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(79, 70, 229, 0);
    }
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-disclaimer h3 {
    color: var(--warm-gold);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.footer-disclaimer p {
    color: #d1d5db;
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-section h4 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.footer-section a {
    display: block;
    color: #d1d5db;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--ai-green);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .hero-container {
        padding: 0 1rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .web3-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Animation Classes */
[data-aos] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}
