「慧视」AI金融洞察引擎 - 网站项目说明

项目概述：
这是一个现代化的AI金融洞察演示网站，以浅色调为主，具有科技感和亲和力。网站展示了AI在股票K线图模式识别和金融报告分析方面的能力，同时暗示了Web3的未来发展方向。

项目结构：
├── index.html          # 首页 - 主要介绍和功能展示
├── demo.html           # AI洞察演示页面 - 核心功能展示
├── about.html          # 关于我们页面 - 理念和愿景
├── css/
│   ├── style.css       # 主要样式文件
│   ├── demo.css        # 演示页面样式
│   └── about.css       # 关于页面样式
├── js/
│   ├── main.js         # 主要JavaScript功能
│   ├── demo.js         # 演示页面交互
│   └── about.js        # 关于页面动画
└── Readme.txt          # 项目说明文件

核心特性：
1. 响应式设计 - 适配各种设备
2. 流畅动画 - 微交互和过渡效果
3. 现代UI - 清洁的设计语言
4. AI演示 - 实时图表和分析展示
5. Web3愿景 - 未来技术展望

技术栈：
- HTML5 语义化标记
- CSS3 现代样式和动画
- JavaScript ES6+ 交互功能
- Chart.js 图表库
- 响应式设计原则

设计理念：
核心主题与氛围 (Overall Theme & Mood):
Bright, clean, futuristic, intelligent, and inviting. The website should convey a sense of insightful clarity, cutting-edge AI technology, and a friendly guide in the complex financial landscape. It's about empowering user understanding through AI exploration.
色彩与光效 (Color Palette & Lighting):
Primary Background: Clean, crisp white or very light off-white (soft gray/cream) to provide a spacious, airy feel.
Accent Colors: Vibrant, yet soft tech-blue (for AI elements, interactive components, borders), refreshing AI-green (for positive trends, successful patterns), and subtle, warm orange/gold gradients (for highlight, emphasis, and a touch of human warmth).
Text: Dark gray or black for primary text, lighter shades for secondary information.
CTAs/Highlights: A clear, inviting tech-blue or warm gold, with a gentle, soft inner glow.
Lighting: Bright, clean, and diffused, emphasizing clarity and information visibility. Subtle, internal glow effects from AI elements or data points for a sophisticated tech feel.
布局与结构 (Layout & Structure):
Minimalist & Open: Ample white space to ensure focus on content, creating a clean, uncrowded look.
Modular & Fluid: Information presented in clean, well-defined cards or sections that feel interconnected and flow naturally.
Intuitive Flow: Clear visual hierarchy guiding the user effortlessly through AI insights.
Responsive: Implied for modern web design.
首页 (Landing Page - Hero Section & Key Features):
Hero Section (首屏)：
Background: Clean, light background. Can include very subtle, abstract, minimalist geometric patterns or a faint, glowing grid that hints at data and AI structure, but is not distracting.
Title (Large, Central): "「慧视」AI金融洞察引擎" (bold, prominent, in tech-blue/orange gradient font).
Subtitle (Below Title): "AI驱动深度学习，发现市场深层模式。" (Crisp, inviting dark text).
Key Value Proposition: A concise sentence or two explaining how AI transforms financial data into clear understanding, emphasizing AI as an exploratory guide.
Primary CTA Button: "探索AI洞察" (Prominent, inviting tech-blue or warm gold, with a subtle glow on hover).
Small Disclaimer: "AI分析仅供技术演示，不构成投资建议。" (Small, light gray text below CTA).
核心功能亮点 (模块化)：
两到三个简洁的模块，每个模块有：
标题： 大字，如“K线模式洞察”、“金融叙事引擎”。
图标： 抽象、干净、科技感的AI相关图标（如：AI大脑、放大镜、数据流符号），配色与主色调统一。
描述： 一句话精炼功能，字体清晰。
微动画： 模块进入视野时有轻微的淡入或上浮动效。
Web3愿景初步展示：
位置： 首页下拉至最下方，一个独立的、简洁的区块。
标题： “探索AI与Web3的未来”
内容： 几行文字，简洁阐述AI洞察如何与Web3（如NFT化、去中心化）结合的愿景，突出“数字资产”的概念。
视觉： 可配一个抽象的区块链/去中心化网络示意图，或一个未来感的、AI生成的抽象艺术品，暗示数字资产的形态。
页脚：
醒目免责声明： 再次强调“本网站内容仅为AI技术演示，不构成任何投资建议。” (字体清晰，非刺激性颜色)。
版权信息，隐私政策链接等。
2. AI洞察演示页面 (核心功能页面)
目标： 直观、流畅地展示AI的K线和报告分析，强调AI的思考过程和洞察升华感。
布局： 顶部股票选择，左侧为K线图组，右侧为AI交互/分析结果。
顶部导航/股票选择器：
左上角：项目Logo与名称。
居中：一个简洁的股票选择下拉菜单 (精选股票列表，设计干净，有微动效)。选择后，页面内容流畅动态更新。
主内容区：
左侧：K线图组 - 平铺展示与微动画
布局： 将日线、周线、月线K线图以上、中、下平铺的形式呈现，每个图表都有一个清晰的框线设计（例如，细致的浅灰色边框或带点微光的科技蓝边框），背景与整体页面统一。
微动画： K线图在加载或刷新时，可以有平滑的渐入动画，或模拟K线图“生长”的微动效，增强“数据生命力”。
AI标注 (核心)：
AI识别到的模式（如锤头线、吞没形态）直接在图表上以清晰的几何形状（例如，半透明的浅科技蓝或浅AI绿区域填充）进行高亮标注。
这些标注在图表上自动突出，可以有轻微的放大效果或柔和的发光效果，但避免使用醒目的红字。
AI识别的支撑/阻力线、趋势线：以细致、柔和的虚线（如浅科技蓝或浅紫）标注在图表上。
图表标题： 明确显示“XX股票日/周/月线图及AI洞察”。
右侧：AI交互/分析结果界面 (这部分作为一个整体的“AI交互界面”)
整体设计： 看起来像一个简洁的AI控制台或AI对话窗口，背景可以是比主背景稍深的浅灰色，有轻微的磨砂或透明感，边缘带一丝科技感的微光。
顶部： 显示“AI洞察摘要”或“「慧视」AI分析”。
内容展示区：
K线模式洞察模块：
“AI识别模式”： 清晰列出AI识别到的K线形态。
“当前趋势洞察”： AI判断的趋势和量价配合分析。
“关键价格区”： AI识别的支撑位和阻力位。
【核心】“历史模式概率推演”：
通过动态、数据流式的可视化（例如：概率条形图内部有细微的粒子流动、渐变色随概率动态填充、或数字在计算完成后平滑出现），展示上涨、下跌、盘整的概率。强调数据在“思考”和“流动”的过程。
旁边配有清晰的百分比数字，字体设计现代。
下方有明确的小字提示：“此为历史统计概率，非未来预测。过去表现不代表未来。”
金融叙事引擎模块：
“报告核心摘要”： AI提炼的简洁摘要，文字清晰易读。
“AI情绪判断”： 一个更高级的情绪可视化。例如，一个流动的、渐变色的能量条（绿到红的柔和渐变），或者一个动态变化的、抽象的情绪图示（如一个波动的心电图或平滑的波浪线），配合文字描述（如“整体情绪：平稳偏乐观”）。
“关键叙事与主题”： 关键词或短语以大小或颜色不同的形式呈现（例如，重要的词更大或颜色更深，形成一个“洞察云”或“焦点关键词”区域）。
用户悬停洞察： (如你建议的，如果可行) 当用户鼠标悬停在某个AI提炼的摘要上时，可以在旁边弹出一个小的、半透明的提示框，高亮显示该摘要在原始报告文本中对应的关键词或短语，以展示AI的溯源能力。
底部免责声明： 在页面底部持续显示，字体清晰，颜色柔和，不喧宾夺主。
3. 关于我们 / AI理念 / Web3愿景页 (About/Vision Page)
布局： 叙事性强，结合少量极简风格的图片/插画。
内容：
“我的AI探索之旅”： 讲述你作为AI探索者的初心、学习历程，以及为什么选择金融领域作为首个项目。
“「慧视」AI的思考哲学”： 阐述AI模型的工作原理（如：如何从海量数据中学习模式，如何通过Prompt Engineering提炼报告精髓），强调“深度”和“透明度”，让AI的思考过程不再是黑箱。
“AI与Web3的未来蓝图”： 深入阐释AI洞察如何与Web3技术（如NFT、去中心化验证）结合，将AI的智慧成果转化为可流通、可验证的数字资产的愿景。可以配上简洁的概念插画。
项目路线图： 简洁明了地罗列未来阶段的规划。
联系方式： 你的联系信息。
Lovable UI/UX 细节与微交互提示：
整体流畅感： 所有页面切换、内容加载、模块展开都应具有极其平滑、流畅的动画。无卡顿感。
加载动画： 当AI进行分析时，显示简洁的几何动画，代表AI的“思考过程”（例如，互相连接的线条和点在流动、变化的图形，颜色与主色调统一）。
统一配色： 确保整个网站的配色方案高度统一，即使是强调色也应在同一色系内，营造和谐的视觉感受。
图标设计： 所有图标都应是定制的、风格统一的扁平化或线条式图标，具有科技感和亲和力。
交互反馈： 按钮、选择器等交互元素在点击或悬停时，应有简洁而明确的视觉反馈（如柔和的发光、边框变化）。
非刺激性提示： 重点提示（如免责声明）使用清晰的字体、柔和的颜色，绝不使用刺眼的红色或闪烁效果。