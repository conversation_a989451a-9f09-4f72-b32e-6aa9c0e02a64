<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 - 「慧视」AI金融洞察引擎</title>
    <meta name="description" content="了解「慧视」AI的创建理念、技术哲学和Web3未来愿景。探索AI如何重新定义金融分析。">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/about.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-icon">
                    <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" stroke="url(#gradient1)" stroke-width="2"/>
                        <path d="M12 20L18 26L28 14" stroke="url(#gradient1)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4F46E5"/>
                                <stop offset="100%" style="stop-color:#06B6D4"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <span class="logo-text">「慧视」AI金融洞察引擎</span>
            </div>
            <div class="nav-links">
                <a href="index.html" class="nav-link">首页</a>
                <a href="demo.html" class="nav-link">AI洞察演示</a>
                <a href="about.html" class="nav-link active">关于我们</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="about-hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">探索AI的无限可能</h1>
                <p class="hero-subtitle">从技术演示到未来愿景，「慧视」AI致力于重新定义金融分析的边界</p>
            </div>
        </div>
    </section>

    <!-- Journey Section -->
    <section class="journey-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">我的AI探索之旅</h2>
                <p class="section-subtitle">从好奇心驱动的学习到技术创新的实践</p>
            </div>
            
            <div class="journey-content">
                <div class="journey-text">
                    <div class="journey-item" data-aos="fade-up">
                        <div class="journey-icon">🚀</div>
                        <div class="journey-info">
                            <h3>初心与启程</h3>
                            <p>作为一名AI探索者，我始终相信人工智能能够为人类带来更深层的洞察。金融市场的复杂性和数据的丰富性，为AI技术提供了完美的试验场。</p>
                        </div>
                    </div>
                    
                    <div class="journey-item" data-aos="fade-up" data-aos-delay="200">
                        <div class="journey-icon">🧠</div>
                        <div class="journey-info">
                            <h3>深度学习之路</h3>
                            <p>通过不断学习机器学习、深度学习和自然语言处理技术，我逐渐掌握了让AI理解和分析复杂金融数据的方法。每一次技术突破都让我更加坚信AI的潜力。</p>
                        </div>
                    </div>
                    
                    <div class="journey-item" data-aos="fade-up" data-aos-delay="400">
                        <div class="journey-icon">💡</div>
                        <div class="journey-info">
                            <h3>选择金融领域</h3>
                            <p>金融市场是一个充满模式和规律的复杂系统。通过AI技术，我们可以发现人类难以察觉的深层模式，为投资者提供更清晰的市场洞察。</p>
                        </div>
                    </div>
                </div>
                
                <div class="journey-visual">
                    <div class="ai-brain">
                        <div class="brain-node" style="--delay: 0s"></div>
                        <div class="brain-node" style="--delay: 0.5s"></div>
                        <div class="brain-node" style="--delay: 1s"></div>
                        <div class="brain-node" style="--delay: 1.5s"></div>
                        <div class="brain-node" style="--delay: 2s"></div>
                        <div class="brain-connection"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Philosophy Section -->
    <section id="vision" class="philosophy-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">「慧视」AI的思考哲学</h2>
                <p class="section-subtitle">透明、深度、可解释的AI分析</p>
            </div>
            
            <div class="philosophy-grid">
                <div class="philosophy-card" data-aos="fade-up">
                    <div class="card-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24" r="20" stroke="url(#philoGradient1)" stroke-width="2"/>
                            <path d="M16 24L20 28L32 16" stroke="url(#philoGradient1)" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="philoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4F46E5"/>
                                    <stop offset="100%" style="stop-color:#06B6D4"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3>深度模式学习</h3>
                    <p>通过分析海量历史数据，AI能够识别出人类难以发现的复杂模式和关联性，从K线形态到市场情绪的微妙变化。</p>
                </div>
                
                <div class="philosophy-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="8" y="8" width="32" height="32" rx="4" stroke="url(#philoGradient2)" stroke-width="2"/>
                            <path d="M16 20L24 28L32 20" stroke="url(#philoGradient2)" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="philoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#10B981"/>
                                    <stop offset="100%" style="stop-color:#06B6D4"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3>透明化分析</h3>
                    <p>我们致力于让AI的思考过程不再是黑箱。每一个分析结果都有清晰的逻辑链条和数据支撑，让用户理解AI的推理过程。</p>
                </div>
                
                <div class="philosophy-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-icon">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="16" r="8" stroke="url(#philoGradient3)" stroke-width="2"/>
                            <path d="M24 24V40" stroke="url(#philoGradient3)" stroke-width="2" stroke-linecap="round"/>
                            <path d="M16 32L32 32" stroke="url(#philoGradient3)" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="philoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#F59E0B"/>
                                    <stop offset="100%" style="stop-color:#EF4444"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <h3>Prompt Engineering</h3>
                    <p>通过精心设计的提示工程，我们能够从复杂的财报中提炼出核心要点，将冗长的文档转化为清晰的洞察。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Web3 Vision Section -->
    <section class="web3-vision-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">AI与Web3的未来蓝图</h2>
                <p class="section-subtitle">将AI智慧转化为可流通的数字资产</p>
            </div>
            
            <div class="vision-content">
                <div class="vision-text">
                    <div class="vision-item" data-aos="fade-right">
                        <div class="vision-number">01</div>
                        <div class="vision-info">
                            <h3>AI洞察NFT化</h3>
                            <p>每一个AI分析结果都将成为独特的数字资产，包含完整的分析过程、数据来源和预测准确性记录。这些NFT不仅是收藏品，更是AI智慧的价值载体。</p>
                        </div>
                    </div>
                    
                    <div class="vision-item" data-aos="fade-right" data-aos-delay="200">
                        <div class="vision-number">02</div>
                        <div class="vision-info">
                            <h3>去中心化验证</h3>
                            <p>通过区块链技术，AI的分析结果将在去中心化网络中得到验证和确认。社区成员可以对AI的预测准确性进行投票和评估，形成可信的AI信誉体系。</p>
                        </div>
                    </div>
                    
                    <div class="vision-item" data-aos="fade-right" data-aos-delay="400">
                        <div class="vision-number">03</div>
                        <div class="vision-info">
                            <h3>智慧资产流通</h3>
                            <p>优质的AI洞察将在去中心化市场中自由流通，形成基于AI智慧的新型经济模式。投资者可以购买、交易和收藏具有历史价值的AI分析结果。</p>
                        </div>
                    </div>
                </div>
                
                <div class="vision-visual">
                    <div class="web3-ecosystem">
                        <div class="ecosystem-center">
                            <div class="ai-core">AI</div>
                        </div>
                        <div class="ecosystem-ring">
                            <div class="ecosystem-node nft">NFT</div>
                            <div class="ecosystem-node blockchain">区块链</div>
                            <div class="ecosystem-node defi">DeFi</div>
                            <div class="ecosystem-node dao">DAO</div>
                        </div>
                        <div class="connection-lines"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Roadmap Section -->
    <section class="roadmap-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">项目路线图</h2>
                <p class="section-subtitle">从技术演示到生态建设的发展规划</p>
            </div>
            
            <div class="roadmap-timeline">
                <div class="timeline-item completed" data-aos="fade-up">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">Phase 1 - 已完成</div>
                        <h3>AI洞察引擎</h3>
                        <p>完成K线模式识别和金融报告分析的核心功能，建立AI分析的基础框架。</p>
                    </div>
                </div>
                
                <div class="timeline-item active" data-aos="fade-up" data-aos-delay="200">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">Phase 2 - 进行中</div>
                        <h3>模型优化与扩展</h3>
                        <p>提升AI模型的准确性，扩展支持更多金融工具和市场，增强实时分析能力。</p>
                    </div>
                </div>
                
                <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">Phase 3 - 规划中</div>
                        <h3>Web3集成</h3>
                        <p>将AI洞察结果NFT化，建立去中心化的AI分析验证和交易平台。</p>
                    </div>
                </div>
                
                <div class="timeline-item" data-aos="fade-up" data-aos-delay="600">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">Phase 4 - 未来愿景</div>
                        <h3>AI智慧生态</h3>
                        <p>构建完整的AI驱动金融生态系统，实现AI智慧的价值化和流通。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-info">
                    <h2>联系我们</h2>
                    <p>如果您对「慧视」AI感兴趣，或希望了解更多技术细节，欢迎与我们联系。</p>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="contact-icon">📧</div>
                            <div class="contact-details">
                                <h4>邮箱联系</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="contact-icon">💬</div>
                            <div class="contact-details">
                                <h4>技术交流</h4>
                                <p>欢迎技术讨论与合作</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="contact-visual">
                    <div class="contact-animation">
                        <div class="floating-element" style="--delay: 0s">🤖</div>
                        <div class="floating-element" style="--delay: 1s">📊</div>
                        <div class="floating-element" style="--delay: 2s">🔮</div>
                        <div class="floating-element" style="--delay: 3s">🚀</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-disclaimer">
                    <h3>重要声明</h3>
                    <p>本网站内容仅为AI技术演示，不构成任何投资建议。所有分析结果仅供学习和研究使用，投资有风险，决策需谨慎。</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>产品</h4>
                        <a href="demo.html">AI洞察演示</a>
                        <a href="index.html#features">核心功能</a>
                    </div>
                    <div class="footer-section">
                        <h4>公司</h4>
                        <a href="about.html">关于我们</a>
                        <a href="about.html#vision">AI理念</a>
                    </div>
                    <div class="footer-section">
                        <h4>联系</h4>
                        <a href="mailto:<EMAIL>">联系我们</a>
                        <a href="#privacy">隐私政策</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 「慧视」AI金融洞察引擎. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/about.js"></script>
</body>
</html>
